#!/usr/bin/env python3
"""
Test script to verify the token extraction logic works correctly
"""

import re
from urllib.parse import urlparse, parse_qs

def test_token_extraction(url):
    """Test the token extraction logic with the provided URL"""
    print(f"Testing URL: {url}")
    print("=" * 80)
    
    # Parse the URL
    parsed_url = urlparse(url)
    query_params = parse_qs(parsed_url.query)
    
    print(f"Found {len(query_params)} query parameters")
    
    found_events = {}
    
    # Look for event_callback_ parameters (main token extraction)
    for param_name, param_values in query_params.items():
        if param_name.startswith('event_callback_'):
            # Extract the 6-character token from parameter name
            token = param_name.replace('event_callback_', '')
            
            # Handle repeated patterns like 'token__token__token'
            if '__' in token:
                token = token.split('__')[0]
            
            # Validate token format (6 alphanumeric characters)
            if len(token) == 6 and token.isalnum():
                param_value = param_values[0] if param_values else ''
                
                # Try to extract goal_id from the callback URL
                goal_id = None
                if param_value:
                    goal_match = re.search(r'goal_id%3D(\d+)', param_value)
                    if goal_match:
                        goal_id = goal_match.group(1)
                
                # Create event entry
                if goal_id:
                    event_key = f"Event: {token} (goal_id: {goal_id})"
                    found_events[event_key] = {"token": token, "goal_id": goal_id}
                    print(f"✓ Found event token: {token} with goal_id: {goal_id}")
                else:
                    event_key = f"Event: {token}"
                    found_events[event_key] = {"token": token}
                    print(f"✓ Found event token: {token}")
            else:
                print(f"⚠ Invalid token format in {param_name}: '{token}' (expected 6 alphanumeric chars)")
    
    # Look for other standard token patterns
    for param_name, param_values in query_params.items():
        if param_values and param_values[0]:
            param_value = param_values[0]
            
            # Check for 6-character tokens as parameter values
            if len(param_value) == 6 and param_value.isalnum() and not param_name.startswith('event_callback_'):
                event_key = f"Event: {param_name} ({param_value})"
                found_events[event_key] = {"token": param_value, "source": "param_value"}
                print(f"✓ Found token as parameter value: {param_name}={param_value}")
            
            # Special handling for tracker_limit
            elif param_name == 'tracker_limit':
                event_key = f"Event: tracker_limit ({param_value})"
                found_events[event_key] = {"token": param_value, "source": "tracker_limit"}
                print(f"✓ Found tracker_limit: {param_value}")
    
    print("\n" + "=" * 80)
    print(f"SUMMARY: Found {len(found_events)} event tokens")
    
    if found_events:
        print("\nExtracted Tokens:")
        for event_name, event_data in found_events.items():
            if isinstance(event_data, dict):
                token = event_data.get('token', 'N/A')
                goal_id = event_data.get('goal_id', '')
                if goal_id:
                    print(f"  • {token} (goal_id: {goal_id})")
                else:
                    print(f"  • {token}")
    
    return found_events

if __name__ == "__main__":
    # Test with the user's example URL
    test_url = "https://app.adjust.com/jkf6kmo?campaign=ahztee-besitos-aos-US-multilevel-20250411&adgroup=1514&cost_type=CPI&cost_amount=6&cost_currency=USD&tracker_limit=250000&creative=mlwm7apcn1zh&click_id=1027118ab3fb7bd29c83d4e2e29ea9&ip_address=************&campaign_id=610&install_callback=kashkick_install%26transaction_id%3D1027118ab3fb7bd29c83d4e2e29ea9&event_callback_kj2ruk=kashkick_event%26goal_id%3D8489%26transaction_id%3D1027118ab3fb7bd29c83d4e2e29ea9&event_callback_slmdmd=kashkick_event%26goal_id%3D8490%26transaction_id%3D1027118ab3fb7bd29c83d4e2e29ea9&event_callback_kifen9=kashkick_event%26goal_id%3D198%26transaction_id%3D1027118ab3fb7bd29c83d4e2e29ea9&event_callback_50kasq=kashkick_event%26goal_id%3D196%26transaction_id%3D1027118ab3fb7bd29c83d4e2e29ea9&event_callback_5vq2el=kashkick_event%26goal_id%3D199%26transaction_id%3D1027118ab3fb7bd29c83d4e2e29ea9&event_callback_652naj=kashkick_event%26goal_id%3D2041%26transaction_id%3D1027118ab3fb7bd29c83d4e2e29ea9&event_callback_zf04mt=kashkick_event%26goal_id%3D204%26transaction_id%3D1027118ab3fb7bd29c83d4e2e29ea9&event_callback_2hoaxr=kashkick_event%26goal_id%3D2042%26transaction_id%3D1027118ab3fb7bd29c83d4e2e29ea9&event_callback_6zzyg4=kashkick_event%26goal_id%3D2043%26transaction_id%3D1027118ab3fb7bd29c83d4e2e29ea9&event_callback_f7k26e=kashkick_event%26goal_id%3D2044%26transaction_id%3D1027118ab3fb7bd29c83d4e2e29ea9&event_callback_cxz850=kashkick_event%26goal_id%3D2045%26transaction_id%3D1027118ab3fb7bd29c83d4e2e29ea9"
    
    results = test_token_extraction(test_url)
    
    print(f"\nTest completed. Found {len(results)} tokens.")
