Aladdin: Your Easy Guide to Mobile Ad Tracking & Offerwall Success
Welcome! This guide will walk you through using Aladdin, our tool designed to make mobile ad tracking and event crediting simple. We'll also dive into some current strategies for popular offerwalls.
Part 1: Understanding Aladdin's Main Screens
Aladdin has three key areas to help you manage everything:
1. The S2S Screen: Crediting In-Game Actions 🎯
What's it for?
This screen is how you tell the ad tracking system (Adjust) that a player did something important in a game (like made a purchase or finished a level). It works for both Android and Apple games.
[IMAGE PLACEHOLDER: Screenshot of the S2S Screen in Aladdin]
How to Use It (Step-by-Step):
1. Pick Your Game:
Use the "Game" dropdown.
2. Pick the Event:
After selecting a game, choose an "Event" like "purchase" or "level_complete."
3. Choose Request Type:
Always pick "POST REQUEST." It's safer and better.
4. Select Platform:
Choose "Android" or "Apple."
5. Enter Device ID:
For Android: Enter the GPS ADID.
For iOS: Enter the IDFA.
Use the "Paste ADID" button if you have it copied!
6. Credit Events:
"Credit Event": Records just the selected event.
"Credit All": Records all available events for the game (use with care).
"Clear": Wipes all your entries to start fresh.
7. View Results:
The "Response Log" shows what happened.
Green = Success! 🎉
Red = Failure. 😥 (The log will show error details).
2. The Attribution Screen: Giving Credit for Installs 🕵️‍♂️
What's it for?
This screen figures out which ad brought a player to install a game. It follows the ad link all the way through to the install.
[IMAGE PLACEHOLDER: Screenshot of the Attribution Screen in Aladdin]
How to Use It (Step-by-Step):
1. Enter Attribution URL:
Paste the full ad link here (e.g., from an ad network, starting with adjust.com, or an app store link).
2. Select Platform:
Choose "Android" or "iOS."
3. Enter Device ID:
Android: GPS ADID.
iOS: IDFA.
You can click "Generate Random ID" for testing.
4. Process Attribution:
Click "Complete Attribution."
5. View Results:
The log shows each step: following links, finding game info, and talking to Adjust.
Green = Good! Red = an issue at that step.
What Happens Behind the Scenes?
Aladdin cleverly follows all link redirects, finds the game's unique ID, and makes several calls to Adjust to record the install attribution, even adding realistic delays like a real phone would!
3. The Token Storage Screen: Your Game & Code Hub 🗄️
What's it for?
This is your central library for all your games and their special "tokens" (codes needed for Adjust to work).
[IMAGE PLACEHOLDER: Screenshot of the Token Storage Screen in Aladdin]
Key Features:
View Game Library:
See game icons, names, app tokens (Green = OK, Red = Missing!), and events.
Search and Filter:
Quickly find games by name, package name, or token.
Filter to see games with specific events (e.g., "purchase").
Sort Options:
Sort by Name, Number of Events, Recently Added.
Helpful Sorts: "Purchase Events" (prioritizes money-making games) or "Missing App Token" (shows games needing setup).
Statistics Cards:
Quick totals for games, events, etc.
Action Buttons:
🔄 Refresh Data: Get the latest game info.
🖼️ Refresh Icons: Re-fetch game icons.
🔍 Search Play Store: Find game details online.
➕ Add New Game: Manually add games.
Game Actions (per game):
👁️ View: See game details (read-only).
✏️ Edit: Change details, add tokens, manage events.
🔗 Store Button: Go to the game's app store page.
Special Highlights:
💰 Purchase events are often highlighted.
Missing App Tokens are clearly marked in red.
Part 2: Bonus - Testing Tips & Useful Tools 🛠️
A. How to "Start Fresh" (Reset Ad ID) on Android for Testing
When testing offers (especially from Offerwalls like JustPlay), you often want to appear as a "new" user. Here’s the typical order:
Delete Apps:
Uninstall the main Offerwall app (e.g., JustPlay).
Uninstall any game apps you downloaded through that Offerwall for testing.
Reset Your Ad ID (Android):
This gives your phone a new advertising identity.
Go to: Settings > Google > Ads > Reset advertising ID.
(Path might vary slightly by Android version, but look for "Google Settings" and then "Ads").
Reinstall Offerwall App:
Go to the Play Store and reinstall the Offerwall app (e.g., JustPlay).
[IMAGE PLACEHOLDER: Screenshot of Android "Reset advertising ID" screen]
B. Handy Tools to Make Life Easier (for Android)
URL Check:
What it does: Shows you all the hidden redirects when you click a link.
Why it's great for Aladdin: When you click an offerwall link, it often jumps through several web pages. URL Check lets you see these jumps and easily copy the actual link you need to put into Aladdin's Attribution screen.
How to use: Set URLCheck as your phone's default browser. When you click a link, it pops up, shows the redirects, and lets you copy the link.
Get it: URL Check on Google Play
[IMAGE PLACEHOLDER: Screenshot of URL Check app interface]
Android Device ID (by AppsFlyer):
What it does: A very simple app that quickly shows and lets you copy your phone's Google Advertising ID (GPS ADID).
Why it's great for Aladdin: You need this ADID for the S2S and Attribution screens. This app is the easiest way to get it.
Get it: Android Device ID on Google Play
[IMAGE PLACEHOLDER: Screenshot of Android Device ID app interface]
Phone Link (from Microsoft):
What it does: Connects your Android phone to your Windows PC. Many devices have this built-in or similar functionality.
Why it's great for Aladdin: Lets you easily copy your ADID (from the app above) or long ad links from your phone and paste them directly into Aladdin on your PC. No more emailing things to yourself!
Get it (if needed): Phone Link on Google Play
[IMAGE PLACEHOLDER: Screenshot or icon of Microsoft Phone Link]
===========================
Part 3: Offerwall Deep Dive & Current Meta
This section covers specific insights for popular offerwalls, particularly within the JustPlay app.
JustPlay (Current Meta Overview)
Understanding Timers:
Many games within JustPlay offers have TIMERS. These are essentially "locks" that prevent you from getting full credit for all events in a game immediately.
You might need to wait for a timer to expire before certain events are credited or before you can "full send" (credit all events) for maximum points.
Some games have timers, some do not. Pay attention to the offer details!
[IMAGE PLACEHOLDER: Example of a game offer in JustPlay showing a timer]
Getting Both Offerwalls (TapJoy & Digital Turbine):
Sometimes, you might only see one offerwall inside JustPlay. You can try to uninstall JustPlay and then reinstall it until you see both the TapJoy (usually green) and Digital Turbine (usually blue) offerwalls.
[IMAGE PLACEHOLDER: Screenshot of JustPlay showing BOTH TapJoy (Green) and Digital Turbine (Blue) offerwall options]
TapJoy (The "Green" Offerwall in JustPlay)
General Expectation: You might aim for around 40-50 million points for the first 3-hour cash-out, which could be around $40. (This can vary!)
Noteworthy TapJoy Offers (check current availability & details in app):
MERGE INN: Potentially INSTANT credit (needs verification).
CLUB VEGAS: May have around a 14.5-HOUR timer for full credit.
IPSY SHOPPING OFFER: Usually INSTANT credit for completing the shopping action.
Note: There are probably more good offers. This list requires ongoing checks as offers change frequently.
[IMAGE PLACEHOLDER: Screenshot of a TapJoy offerwall list within JustPlay]
Digital Turbine (The "Blue" Offerwall in JustPlay)
General Notes: Currently, there might be a few instant offers. However, many high-reward games on Digital Turbine have 5 to 7-DAY TIMERS before you get the big points. We have many of these games in Aladdin's Token Storage.
Instant / Short Timer Digital Turbine Offers (check current availability & details):
NOVA SOLITAIRE: Often INSTANT credit.
BENJAMIN: This is an app within an app. You complete offers inside the Benjamin app to get rewards on the Digital Turbine offer in JustPlay.
PARKING JAM: If you can find this offer, it might be pushable for full credit about 30 MINUTES after install.
DIAMOND CITY: Look for the offer with only 4 events, specifically one event named "DINO PARK." This may have around a 4.5-HOUR timer before crediting.
Games with 5-7 DAY TIMERS on Digital Turbine (High Reward Potential):
Important Tip: Most of these games have two different offers. Always go for the bigger offer payout one (generally around 50 million - 70 million points).
We have many of these games in Aladdin. Key ones to look for:
SOLITAIRE DELUXE 2
SPIDER SOLITAIRE DELUXE 2
SCATTER SLOTS
CLUBILLION SLOTS
MERGE TOWN - DESIGN FARM
CRYPTO MINER TYCOON
JACKPOTLAND
NUT SORT: COLOR SORTING GAME
JACKPOTWILD
TRAVEL MATCH
[IMAGE PLACEHOLDER: Screenshot of a Digital Turbine offerwall list within JustPlay, maybe highlighting a game with a long timer]
This guide should give you a solid start with Aladdin and navigating common offerwall scenarios. Remember that offerwall details can change, so always check the specifics within the JustPlay app!
Good luck!